# encoding=utf-8
"""
小红书-官方笔记数据详情
"""
import datetime

from xhs_apis import apis
from xhs_data.database import database
from xhs_data.database import models
from utils.logx import logger
from xhs_data.utils.get_time import get_time

db = database.create_app()


def pagin(func):
    """
        分页装饰器
    """

    def wrapper(self, page, *args, **kwargs):
        is_next = func(self, page, *args, **kwargs)
        while is_next:
            page += 1
            is_next = func(self, page, *args, **kwargs)

    return wrapper


class XhsGoodsNoteDetail:
    def __init__(self, time_type="昨天", tool=None):
        self.spider_at = datetime.datetime.now()
        self.tool = tool
        if tool == '全部笔记':
            time_type = '过去30天'
        args = {"time_type": time_type, "timestamp_type": "秒", "time_format": "%Y-%m-%d"}
        get_time(args)
        self.time_tuple = args['time_tuple']
        logger.info('XhsGoodsNoteDetail-获取时间为:', self.time_tuple)
        self.deleted_at = {"deleted_at": datetime.datetime.now()}

    def get_xhs_goods_note_detail(self):
        tool = self.tool
        if tool == '全部笔记':
            params = {
                'dateType': '3',
                'orderField': 'readNum',
                'noteDataType': '2',
                'pageSize': '100',
            }
        elif tool == '商品笔记':
            params = {
                'dateType': '0',
                'startDate': self.time_tuple[0],
                'endDate': self.time_tuple[1],
                'orderField': 'readNum',
                'noteDataType': '1',
                'pageSize': '100',
            }
        else:
            logger.error(f'小红书-{self.tool}-工具类型错误')
            return
        self.request_xhs_goods_note_detail(page=1, tool=tool, params=params)
        logger.info(f'小红书-{self.tool}数据详情-获取完成')

    @pagin
    def request_xhs_goods_note_detail(self, page, params, tool):
        params['pageNo'] = page
        is_success, data = apis.XhsSpider().get_goods_note_detail(param=params)
        if not is_success:
            return False
        rows = data.get('data', {}).get("data", [])
        if not rows:
            return False
        self.dispose_data(tool=tool, rows=rows)
        count = data.get('data', {}).get("count", 0)
        if count > page * 100:
            return True
        return False

    def dispose_data(self, tool, rows):
        insert_data = []
        for row in rows:
            note_info = row.get('noteInfo', {})
            note = row.get('note', {})
            engage = row.get('engage', {})
            trade = row.get('trade', {})
            insert_item = {
                "spider_at": self.spider_at,
                "start_date": datetime.datetime.strptime(self.time_tuple[0], '%Y-%m-%d'),  # 开始时间
                "end_date": datetime.datetime.strptime(self.time_tuple[1], '%Y-%m-%d') + datetime.timedelta(
                    hours=23, minutes=59, seconds=59),  # 结束时间
                'note_title': note_info.get('noteName'),  # 商品笔记名称
                'note_id': note_info.get('noteId'),  # 商品笔记ID
                'note_url': f'https://www.xiaohongshu.com/discovery/item/{note_info.get("noteId")}',  # 商品笔记链接
                'kol_nick_name': note_info.get('notePublisherName'),  # 作者昵称
                'note_publish_time': note_info.get('notePublishTime'),  # 笔记发布时间
                'read_num': note.get("readNum"),  # 阅读次数
                'like_num': note.get("likeNum"),  # 点赞次数
                'fav_num': note.get("favNum"),  # 收藏次数
                'cmt_num': note.get("cmtNum"),  # 评论次数
                'share_num': note.get("shareNum"),  # 分享次数
                'goods_view_num': engage.get("goodsView"),  # 引导商品点击
                'add_cart_pv': engage.get("addCartPv"),  # 引导商品加购
                'add_fan_num': engage.get("fans"),  # 新增粉丝数
                'goods_click_rate': engage.get("goodsClickRate"),  # 引导商品点击率
                'goods_pv_prt': engage.get("goodsPvPrt"),  # 商品点击占比
                'note_pay_gmv': trade.get('notePayGmv'),  # 笔记支付金额
                'note_pay_order_cnt': trade.get('notePayOrderCnt'),  # 笔记支付订单数
                'gmv_prt': trade.get('gmvPrt'),  # 支付金额占比
                'avg_read_views': note.get('avgReadViews'),  # 平均阅读时长(s)
                'upr': trade.get('upr'),  # 笔记支付转化率
                'dan_num': note.get('danmakuNum'),  # 弹幕次数
                'tool': tool
            }
            insert_data.append(insert_item)
            need_fields = ["spider_at", 'note_title', 'note_url', 'kol_nick_name', 'note_publish_time',
                           'read_num', 'like_num', 'fav_num', 'cmt_num', 'share_num',
                           'goods_view_num', 'add_cart_pv', 'add_fan_num', 'goods_click_rate',
                           'goods_pv_prt', 'note_pay_gmv', 'note_pay_order_cnt', 'gmv_prt',
                           'avg_read_views', 'upr']
            models.DasApplicationXhsGoodsNoteDetail.bulk_insert_on_duplicate(db=db, data=insert_data,
                                                                             update_keys=need_fields)

    def run(self):
        self.get_xhs_goods_note_detail()


if __name__ == '__main__':
    # tool = '商品笔记'
    tool = '全部笔记'
    xhs_goods_note_detail = XhsGoodsNoteDetail(tool=tool)
    xhs_goods_note_detail.run()
