# encoding=utf-8
"""
小红书 - 粉丝数据
"""
from datetime import datetime, timedelta, date
from xhs_apis.apis import XhsSpider
from utils.logx import logger
from utils.dingtalk import DingTalk
from xhs_data.settings import config
from utils.encryption import AesEncryption
from xhs_data.database import database
from xhs_data.database import models

db = database.create_app()


class XhsFansData:
    def __init__(self):
        self.spider_at = datetime.now()
        logger.info('XhsFansData-获取时间为:', self.spider_at)
        self.ding_talk = DingTalk(webhook=config.get("ding_talk").get("webhook"),
                                  secret=AesEncryption(config.get("secret_key")).decryption(
                                      (config.get("ding_talk").get("secret")))
                                  )

    def get_xhs_fans_data(self):

        payload = {
            "requestBody": {
                "blockElements": [
                    {"blockKey": "fansInfoProCenterTrend", "filterMap": {"dateType": 3}}
                ]
            }
        }
        is_success, data = XhsSpider().get_fans_data(payload=payload)
        if not is_success:
            logger.info('XhsFansData-获取小红书粉丝数据失败')
            return False
        res_data = data.get('data', [])
        if not res_data:
            logger.info('XhsFansData-获取小红书粉丝数据为空')
            return False
        rows = res_data[0].get('data', [])
        insert_list = []

        for row in rows:
            insert_dict = {
                'spider_at': self.spider_at,
                'dtm': row.get('dtm').get('value'),  # 日期
                'fans_num': row.get('fansNum').get('value', 0),  # 粉丝数
                'add_fans_num': row.get('addFansNum').get('value', 0),  # 新增粉丝数
                'lost_fans_num': row.get('lostFansNum').get('value', 0),  # 流失粉丝数
            }
            insert_list.append(insert_dict)
        models.DasApplicationXhsFansData.bulk_insert_on_duplicate(db=db, data=insert_list,
                                                                  update_keys=['spider_at',
                                                                               'fans_num',
                                                                               'add_fans_num',
                                                                               'lost_fans_num'
                                                                               ])
        logger.info('XhsFansData-获取小红书粉丝数据成功')


if __name__ == '__main__':
    XhsFansData().get_xhs_fans_data()
