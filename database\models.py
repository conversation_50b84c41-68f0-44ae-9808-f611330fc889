# coding: utf-8
import time
from sqlalchemy import Column, DateTime, Integer, text, event, orm, DECIMAL, Date, Index
from sqlalchemy.dialects.mysql import INTEGER, VARCHAR, insert
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import and_
from sqlalchemy.orm import Session

_Base = declarative_base()
metadata = _Base.metadata


class BaseModel(_Base):
    __abstract__ = True

    id = Column(INTEGER(11), primary_key=True)
    created_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    updated_at = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
                        comment='更新时间')
    deleted_at = Column(DateTime, comment='删除时间')
    remark = Column(VARCHAR(55), comment='备注')

    @staticmethod
    def get_dict_data(data: list):
        return [dict(zip(item.keys(), item)) for item in data]

    @classmethod
    def insert(cls, db, item):
        db.add(item)
        db.flush()
        return item


@event.listens_for(BaseModel, 'before_insert')
def receive_before_create(mapper, connection, target):
    """listen for the 'before_insert' event"""
    target.created_at = time.strftime('%Y-%m-%d %H:%M:%S')
    target.updated_at = time.strftime('%Y-%m-%d %H:%M:%S')


@event.listens_for(BaseModel, 'before_update')
def receive_before_update(mapper, connection, target):
    """listen for the 'before_update' event"""
    target.updated_at = time.strftime('%Y-%m-%d %H:%M:%S')


@event.listens_for(Session, "do_orm_execute")
def _add_filtering_criteria(execute_state):
    if (
            not execute_state.is_column_load and not execute_state.is_relationship_load and not execute_state.execution_options.get(
        "include_private", False)):
        execute_state.statement = execute_state.statement.options(orm.with_loader_criteria(
            BaseModel,
            lambda cls: cls.deleted_at == None,
            include_aliases=True,
        ))


class XhsGrassArticlesInfo(BaseModel):
    __tablename__ = 'das_application_xhs_grass_articles'

    spider_at = Column(DateTime, comment='采集时间')
    data_update_date = Column(Date, comment='数据更新时间')
    kol_nick_name = Column(VARCHAR(255), comment='博主昵称')
    kol_id = Column(VARCHAR(55), comment='博主id')
    kol_url = Column(VARCHAR(255), comment='博主主页链接')
    kol_fan_num = Column(VARCHAR(55), comment='博主粉丝数')
    kol_credit_level = Column(VARCHAR(55), comment='博主信用等级')
    note_title = Column(VARCHAR(55), comment='笔记标题')
    note_url = Column(VARCHAR(255), comment='笔记链接')
    note_type = Column(VARCHAR(55), comment='笔记类型')
    note_publish_time = Column(DateTime, comment='笔记发布时间')
    level = Column(VARCHAR(55), comment='笔记来源')
    note_id = Column(VARCHAR(55), comment='笔记id')
    collection_type = Column(VARCHAR(55), comment='内容标签')
    biz_title = Column(VARCHAR(55), comment='合作名称')
    operate_user_name = Column(VARCHAR(55), comment='下单账号')
    kol_price = Column(DECIMAL(20, 2), comment='博主报价')
    total_platform_price = Column(DECIMAL(20, 2), comment='服务费金额')
    spu_name = Column(VARCHAR(55), comment='spu名称')
    imp_num = Column(INTEGER(11), comment='曝光量')
    read_num = Column(INTEGER(11), comment='阅读量')
    read_uv_num = Column(INTEGER(11), comment='阅读UV')
    video_play_5s_rate = Column(DECIMAL(20, 2), comment='5s播放率')
    pic_read_3s_rate = Column(DECIMAL(20, 2), comment='3s阅读率')
    duration = Column(VARCHAR(55), comment='视频总时长')
    avg_viewtimed = Column(DECIMAL(20, 2), comment='平均浏览时长')
    engage_num = Column(INTEGER(11), comment='互动量')
    engage_rate = Column(DECIMAL(20, 2), comment='互动率')
    like_num = Column(INTEGER(11), comment='点赞量')
    fav_num = Column(INTEGER(11), comment='收藏量')
    cmt_num = Column(INTEGER(11), comment='评论量')
    share_num = Column(INTEGER(11), comment='分享量')
    follow_num = Column(INTEGER(11), comment='分享量')
    origin_imp_num = Column(INTEGER(11), comment='自然曝光量')
    origin_read_num = Column(INTEGER(11), comment='自然阅读量')
    ads_imp_num = Column(INTEGER(11), comment='推广曝光量')
    ads_effective_read_num = Column(INTEGER(11), comment='推广阅读量')
    heat_imp_num = Column(INTEGER(11), comment='加热曝光量')
    heat_read_num = Column(INTEGER(11), comment='加热阅读量')
    note_bottom_compType = Column(VARCHAR(10), comment='笔记栏底组织类型')
    note_bottom_comp_imp_num = Column(Integer, comment='笔记栏组件曝光量')
    note_bottom_comp_click_pv_num = Column(Integer, comment='笔记底栏组件点击量')
    note_bottom_comp_click_uv_num = Column(Integer, comment='笔记底栏组件点击人数')
    note_bottom_comp_click_ratio = Column(DECIMAL(10, 5), comment='笔记底栏组件CTR')
    shop_name = Column(VARCHAR(50), comment='店铺名称')

    @classmethod
    def update(cls, db, to_day, yes_day, data):
        db.query(cls).filter(
            and_(
                XhsGrassArticlesInfo.created_at >= to_day,
                XhsGrassArticlesInfo.data_update_date == yes_day
            )
        ).update(data)

    @classmethod
    def select_gt_le_info(cls, db, to_day, yes_day):
        item = db.query(cls).filter(
            and_(
                XhsGrassArticlesInfo.created_at >= to_day,
                XhsGrassArticlesInfo.data_update_date == yes_day
            )
        ).all()
        return item


class XhsKeyWord(BaseModel):
    __tablename__ = 'das_application_xhs_keyword'

    spider_at = Column(DateTime, comment='采集时间')
    search_keyword = Column(VARCHAR(55), comment='搜索关键词')
    keyword = Column(VARCHAR(55), comment='关键词')
    recommend_reason = Column(VARCHAR(255), comment='推荐理由')
    competition_level = Column(VARCHAR(55), comment='竞争指数')
    month_pv = Column(VARCHAR(55), comment='月均搜索指数')
    bid = Column(DECIMAL(20, 2), comment='竞价指数')
    source = Column(VARCHAR(55), comment='来源')
    competition_score = Column(DECIMAL(20, 2), comment='竞争力')
    soc = Column(DECIMAL(20, 2), comment='搜索指数')
    tool = Column(VARCHAR(55), comment='工具')

    @classmethod
    def update(cls, db, to_day, search_keyword, data, tool):
        db.query(cls).filter(
            and_(
                XhsKeyWord.created_at >= to_day,
                XhsKeyWord.search_keyword.in_(search_keyword),
                XhsKeyWord.tool == tool
            )
        ).update(data)

    @classmethod
    def select_gt_le_info(cls, db, to_day, search_keyword, tool):
        item = db.query(cls).filter(
            and_(
                XhsKeyWord.created_at >= to_day,
                XhsKeyWord.search_keyword.in_(search_keyword),
                XhsKeyWord.tool == tool
            )
        ).all()
        return item


class DasApplicationXhsGoodsDetail(BaseModel):
    __tablename__ = 'das_application_xhs_goods_details'
    __table_args__ = (
        Index('udx_xhs_goods_date_id_channel', 'start_date', 'end_date', 'goods_id', 'entrance_channel', unique=True),
        {'comment': '小红书站内总成交'}
    )

    start_date = Column(DateTime, nullable=False, comment='起始日期')
    end_date = Column(DateTime, nullable=False, comment='结束日期')
    goods_name = Column(VARCHAR(255), comment='关联商品标题')
    goods_id = Column(VARCHAR(55), nullable=False, comment='商品ID')
    goods_note_pay_money = Column(DECIMAL(20, 3), comment='笔记支付金额')
    goods_live_pay_money = Column(DECIMAL(20, 3), comment='直播支付金额')
    goods_card_pay_money = Column(DECIMAL(20, 3), comment='商品卡支付金额')
    pay_money = Column(DECIMAL(20, 3), comment='支付金额')
    entrance_channel = Column(VARCHAR(255), comment='渠道名称')
    goods_uv = Column(Integer, comment='商品访客数')
    cart_user_num = Column(Integer, comment='新增加购人数')
    pay_user_num = Column(Integer, comment='支付买家数')

    @classmethod
    def bulk_insert_on_duplicate(cls, db, data, update_keys):
        insert_stmt = insert(cls).values(data)
        update_columns = {x.name: x for x in insert_stmt.inserted if x.name in update_keys}
        update_columns['deleted_at'] = insert_stmt.inserted.deleted_at
        on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**update_columns)
        db.execute(on_duplicate_key_stmt)
        db.commit()


class DasApplicationXhsPutCost(BaseModel):
    __tablename__ = 'das_application_xhs_put_cost'
    __table_args__ = {'comment': '小红书投流花费'}

    start_date = Column(DateTime, nullable=False, comment='起始日期')
    end_date = Column(DateTime, nullable=False, comment='结束日期')
    account_id = Column(VARCHAR(55), comment='投放账号id')
    account_name = Column(VARCHAR(55), comment='投放账号名称')
    market_target = Column(VARCHAR(55), comment='营销目标')
    note_time = Column(Date, comment='笔记日期')
    note_id = Column(VARCHAR(55), comment='笔记id')
    note_title = Column(VARCHAR(255), comment='笔记标题')
    pay_money = Column(DECIMAL(20, 3), comment='支付金额')
    _7_gmv = Column('7_gmv', DECIMAL(20, 3), comment='7日下单金额')
    impression_num = Column(Integer, comment='展现量')
    click_num = Column(Integer, comment='点击量')
    click_num_ratio = Column(DECIMAL(20, 3), comment='点击率')
    acp_num = Column(DECIMAL(20, 3), comment='平均点击成本')
    cpm_num = Column(DECIMAL(20, 3), comment='平均千次展示费用')
    like_num = Column(Integer, comment='点赞')
    comment_num = Column(Integer, comment='评论')
    collect_num = Column(Integer, comment='收藏')
    follow_num = Column(Integer, comment='关注')
    share_num = Column(Integer, comment='分享')
    interaction_num = Column(Integer, comment='互动量')
    cpi = Column(DECIMAL(20, 3), comment='平均互动成本')
    action_button_click = Column(Integer, comment='行动按钮点击量')
    action_button_ctr = Column(DECIMAL(20, 3), comment='行动按钮点击率')
    screenshot_num = Column(Integer, comment='截图')

    @classmethod
    def update(cls, db, start_date, end_date, account_id,target_name, data):
        db.query(cls).filter(
            and_(
                cls.note_time >= start_date,
                cls.note_time <= end_date,
                cls.market_target == target_name,
                cls.account_id == account_id
            )
        ).update(data)


class DasApplicationXhsGoodsNoteDetail(BaseModel):
    __tablename__ = 'das_application_xhs_goods_note_detail'
    __table_args__ = (
        Index('udx_xhs_goods_note_date_id', 'start_date', 'note_id', 'end_date', 'tool', unique=True),
        {'comment': '小红书官方笔记数据'}
    )
    spider_at = Column(DateTime, comment='采集时间')
    start_date = Column(DateTime, nullable=False, comment='起始日期')
    end_date = Column(DateTime, nullable=False, comment='结束日期')
    note_title = Column(VARCHAR(255), comment='笔记名称')
    note_id = Column(VARCHAR(55), comment='笔记id')
    note_url = Column(VARCHAR(255), comment='笔记链接')
    kol_nick_name = Column(VARCHAR(255), comment='作者昵称')
    note_publish_time = Column(DateTime, comment='笔记发布时间')
    read_num = Column(Integer, comment='阅读次数')
    like_num = Column(Integer, comment='点赞次数')
    fav_num = Column(Integer, comment='收藏次数')
    cmt_num = Column(Integer, comment='评论次数')
    share_num = Column(Integer, comment='分享次数')
    goods_view_num = Column(Integer, comment='引导商品点击')
    add_cart_pv = Column(Integer, comment='引导商品加购')
    add_fan_num = Column(Integer, comment='新增粉丝数')
    goods_click_rate = Column(DECIMAL(20, 3), comment='商品点击率')
    goods_pv_prt = Column(DECIMAL(20, 3), comment='商品点击占比')
    note_pay_gmv = Column(DECIMAL(20, 3), comment='笔记支付金额')
    note_pay_order_cnt = Column(Integer, comment='新增粉丝数')
    gmv_prt = Column(DECIMAL(20, 3), comment='商品点击占比')
    avg_read_views = Column(DECIMAL(20, 3), comment='平均阅读时长(s)')
    upr = Column(DECIMAL(20, 3), comment='笔记支付转化率')
    dan_num = Column(Integer, comment='弹幕次数')
    tool = Column(VARCHAR(55), comment='工具')

    @classmethod
    def bulk_insert_on_duplicate(cls, db, data, update_keys):
        sql = """
        ALTER TABLE %s auto_increment=1;
        		""" % (cls.__tablename__)
        # res = db.execute(sql)
        insert_stmt = insert(cls).values(data)
        update_columns = {x.name: x for x in insert_stmt.inserted if x.name in update_keys}
        update_columns['deleted_at'] = insert_stmt.inserted.deleted_at
        on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**update_columns)
        db.execute(on_duplicate_key_stmt)
        db.execute(sql)
        db.commit()


class DasApplicationXhsFansData(BaseModel):
    __tablename__ = 'das_application_xhs_fans_data'

    spider_at = Column(DateTime, comment='采集时间')
    dtm = Column(Date, nullable=False, unique=True, comment='日期')
    fans_num = Column(Integer, nullable=False, comment='粉丝数')
    add_fans_num = Column(Integer, nullable=False, comment='新增粉丝数')
    lost_fans_num = Column(INTEGER, nullable=False, comment='流失粉丝数')

    @classmethod
    def bulk_insert_on_duplicate(cls, db, data, update_keys):
        sql = """
            ALTER TABLE %s auto_increment=1;
            		""" % (cls.__tablename__)
        # res = db.execute(sql)
        insert_stmt = insert(cls).values(data)
        update_columns = {x.name: x for x in insert_stmt.inserted if x.name in update_keys}
        update_columns['deleted_at'] = insert_stmt.inserted.deleted_at
        on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**update_columns)
        db.execute(on_duplicate_key_stmt)
        db.execute(sql)
        db.commit()


class DasApplicationXhsSiteKeyword(BaseModel):
    __tablename__ = 'das_application_xhs_site_keyword'
    __table_args__ = (
        Index('idx_date_search_key', 'date_key', 'search_keyword', unique=True),
        {'comment': '小红书全站数据搜索词'}
    )

    search_keyword = Column(VARCHAR(55), comment='搜索关键词')
    date_key = Column(DateTime, comment='日期')
    search_score = Column(Integer, comment='搜索指数')
    imp_num = Column(Integer, comment='关联曝光量')
    read_num = Column(Integer, comment='关联阅读量')
    engage_num = Column(Integer, comment='关联互动量')
    node_num = Column(Integer, comment='关联笔记数')
    user_num = Column(Integer, comment='关联作者量')

    @classmethod
    def bulk_insert_on_duplicate(cls, db, data, update_keys):
        sql = """
                ALTER TABLE %s auto_increment=1;
                		""" % (cls.__tablename__)
        # res = db.execute(sql)
        insert_stmt = insert(cls).values(data)
        update_columns = {x.name: x for x in insert_stmt.inserted if x.name in update_keys}
        update_columns['deleted_at'] = insert_stmt.inserted.deleted_at
        on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**update_columns)
        db.execute(on_duplicate_key_stmt)
        db.execute(sql)
        db.commit()