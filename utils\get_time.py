import datetime
import time
import re
import socket

now = datetime.date.today()


def get_time(args):
    """获取对应时间区间或时间戳

    Args:
        time_type (str): 输入时间类型，可选：今天、昨天、本周、上周、本月、上月、最近7天、本季度、今年
        time_format (str, optional): 时间格式化格式. Defaults to '%Y-%m-%d'.
        timestamp_is (bool, optional): 是否返回时间戳. Defaults to False.
        timestamp_type (str, optional): 输入时间戳类型，可选：秒、毫秒、微秒. Defaults to "秒".

    Returns:
        _type_: 返回时间区间
    """
    time_type = str(args.get("time_type","昨天"))
    if args.get("time_format"):
        time_format = str(args.get("time_format","%Y-%m-%d"))
    else:
        time_format = "%Y-%m-%d"
    timestamp_is = args.get("timestamp_is",False)
    timestamp_type = str(args.get("timestamp_type","秒"))
    if time_type == '今天':
        # 今天
        start_time = datetime.datetime(now.year, now.month, now.day)
        end_time = datetime.datetime(now.year, now.month, now.day) + datetime.timedelta(hours=23, minutes=59, seconds=59)
    elif time_type == '昨天' or time_type == "昨日":
        # 昨天
        yesterday = now - datetime.timedelta(days=1)
        start_time = datetime.datetime(yesterday.year, yesterday.month, yesterday.day)
        end_time = datetime.datetime(yesterday.year, yesterday.month, yesterday.day) + datetime.timedelta(hours=23, minutes=59, seconds=59)
    elif time_type == '前天':
        # 前天
        day = now - datetime.timedelta(days=2)
        start_time = datetime.datetime(day.year, day.month, day.day)
        end_time = datetime.datetime(day.year, day.month, day.day) + datetime.timedelta(hours=23, minutes=59, seconds=59)
    elif time_type == '大前天':
        # 大前天
        day = now - datetime.timedelta(days=3)
        start_time = datetime.datetime(day.year, day.month, day.day)
        end_time = datetime.datetime(day.year, day.month, day.day) + datetime.timedelta(hours=23, minutes=59, seconds=59)
    elif time_type == '本周':
        # 本周第一天和最后一天
        this_week_start = now - datetime.timedelta(days=now.weekday())
        this_week_end = now + datetime.timedelta(days=6 - now.weekday())
        start_time = datetime.datetime(this_week_start.year, this_week_start.month, this_week_start.day)
        end_time = datetime.datetime(this_week_end.year, this_week_end.month, this_week_end.day) + datetime.timedelta(hours=23, minutes=59, seconds=59)
    elif time_type == '上周':
        # 上周第一天和最后一天
        start_time = now - datetime.timedelta(days=now.weekday() + 7)
        end_time = datetime.datetime(start_time.year, start_time.month, start_time.day) + datetime.timedelta(days=6) + datetime.timedelta(hours=23, minutes=59, seconds=59)
    elif time_type == '本月':
        # 本月第一天和最后一天
        start_time = datetime.datetime(now.year, now.month, 1)
        end_time = datetime.datetime(now.year, now.month , now.day) - datetime.timedelta(days=1) + datetime.timedelta(hours=23, minutes=59, seconds=59)
    elif time_type == '上月':
        # 上月第一天和最后一天
        start_time = (datetime.datetime(now.year, now.month, 1) - datetime.timedelta(days=1)).replace(day=1)
        end_time = datetime.datetime(now.year, now.month, 1) - datetime.timedelta(days=1) + datetime.timedelta(hours=23, minutes=59, seconds=59)
    elif re.search(r"过去\d+", time_type):
        # 最近time_num天
        start_time = now - datetime.timedelta(days=int(re.search(r"\d+", time_type).group()))
        end_time = datetime.datetime(now.year, now.month, now.day) - datetime.timedelta(days=1) + datetime.timedelta(hours=23, minutes=59, seconds=59)
    elif time_type == '本季度':
        # 本季第一天和最后一天
        start_time = datetime.datetime(now.year, (now.month - 1) // 3 * 3 + 1, 1)
        end_time = datetime.datetime(now.year, (now.month - 1) // 3 * 3 + 4, 1) - datetime.timedelta(days=1) + datetime.timedelta(hours=23, minutes=59, seconds=59)
    elif time_type == '今年':
        # 本年第一天和最后一天
        start_time = datetime.datetime(now.year, 1, 1)
        end_time = datetime.datetime(now.year + 1, 1, 1) - datetime.timedelta(days=1) + datetime.timedelta(hours=23, minutes=59, seconds=59)
    elif "," in time_type:
        time_list = time_type.split(',')
        if len(time_list) == 2:
            start_time_str= time_list[0].replace("\"", "").replace("\'", "").replace("(", "").replace(" ", "")
            end_time_str= time_list[1].replace("\"", "").replace("\'", "").replace(")", "").replace(" ", "")
            start_time = datetime.datetime.strptime(start_time_str, "%Y-%m-%d")
            end_time = datetime.datetime.strptime(end_time_str, "%Y-%m-%d") + datetime.timedelta(hours=23, minutes=59, seconds=59)
        else:
            return None, None
    elif re.search("\d{4}-\d{2}-\d{2}", time_type):
        start_time = datetime.datetime.strptime(time_type, "%Y-%m-%d")
        end_time = datetime.datetime.strptime(time_type, "%Y-%m-%d") + datetime.timedelta(hours=23, minutes=59, seconds=59)
    else:
        return None, None



    if timestamp_is:
        start_time_timestamp = time.mktime(start_time.timetuple())
        end_time_timestamp = time.mktime(end_time.timetuple())
        if timestamp_type == "秒":
            args["time_tuple"] = int(start_time_timestamp), int(end_time_timestamp)
        elif timestamp_type == "毫秒":
            args["time_tuple"] = int(start_time_timestamp * 1000), int(end_time_timestamp * 1000)
        elif timestamp_type == "微秒":
            args["time_tuple"] = int(start_time_timestamp * 1000000), int(end_time_timestamp * 1000000)
        else:
            args["time_tuple"] = start_time_timestamp, end_time_timestamp
    else:
        args["time_tuple"] = start_time.strftime(time_format), end_time.strftime(time_format)