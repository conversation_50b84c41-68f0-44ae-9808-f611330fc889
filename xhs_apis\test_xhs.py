import json
import unittest
import warnings

warnings.simplefilter(action='error', category=ResourceWarning)
from xhs_data.xhs_apis import apis


class TestCases(unittest.TestCase):

    @classmethod
    def setUpClass(cls) -> None:
        """
        所有用例的前置
        @return:
        """
        pass

    @classmethod
    def tearDownClass(cls) -> None:
        """
        所有用例的后置
        @return:
        """
        pass

    def setUp(self) -> None:
        """
        每个用例前置
        @return:
        """
        pass

    def tearDown(self) -> None:
        """
        每个用例的后置
        @return:
        """
        pass

    def test_grass_articles(self):
        """
        小红书种草文章接口
        """
        # payload = {"operatorUserIds": [], "brandUserIds": [], "kolIds": [], "cooperateType": [], "dateType": "2",
        #            "bizTitle": "", "keyword": "", "noteId": "", "startTime": "2023-11-03", "endTime": "2023-11-05",
        #            "pageNum": 1, "pageSize": 10, "sorts": [], "spuIds": [], "collectionTypeList": []}
        payload = {"operatorUserIds": [], "brandUserIds": [], "kolIds": [], "cooperateType": [], "dateType": "2",
                   "bizTitle": "", "keyword": "", "noteId": "", "pageNum": 1, "pageSize": 200, "sorts": [],
                   "spuIds": [],
                   "collectionTypeList": []}

        is_success, res = apis.XhsSpider().get_grass_articles(payload=payload)

        print(f"用例执行：test_grass_articles: {res}")  # 启动任务

    def test_get_keyword_search(self):
        """
        小红书关键词搜索接口
        """
        payload = {
            "requestType": "search",
            "priceUplift": 0,
            "keyword": "宝宝馋了",
            "highEffectRec": 0
        }
        is_success, res = apis.XhsSpider().get_keyword_search(payload=payload)
        print(f"用例执行：test_get_keyword_search: {res}")

    def test_get_seller_carrier_item(self):
        """
        XHS商品详情接口
        """
        params = {
            # 'type': 'sellerCarrierItemDataList',
            # 'type': 'sellerCarrierItemGmvOverall',
        }
        json_data = {
            "requestBody": {"blockElements": [
                {
                    'filterMap': {
                        'dateType': 0,
                        'startDate': '2024-01-01',
                        'endDate': '2024-01-31',
                        'itemId': '63ecbb6ebc9ab20001c99781',
                    },
                    'page': 1,
                    'size': 100,
                    'blockKey': 'sellerCarrierItemDataList',
                },
                # {
                #     "blockKey": "sellerCarrierItemGmvOverall",
                #     "filterMap": {
                #         "dateType": 0,
                #         "startDate": "2024-01-01",
                #         "endDate": "2024-01-31",
                #         "itemId": "63ecbb6ebc9ab20001c99781",
                #         "skuId": "holder"
                #     }
                # },
                # {
                #     "blockKey": "sellerCarrierItemGmvTrend",
                #     "filterMap": {
                #         "dateType": 0,
                #         "startDate": "2024-01-01",
                #         "endDate": "2024-01-31",
                #         "itemId": "63ecbb6ebc9ab20001c99781",
                #         "skuId": "holder"
                #     }
                # }
            ]}
        }
        is_success, res = apis.XhsSpider().get_seller_carrier_item(params=params, payload=json_data)
        print(f"用例执行：test_get_keyword_search: {res}")

    def test_get_put_cost(self):
        json_data = {'vSellerId': '6524b4d07fa15200013b87b4',
                     'columns': ['noteId', 'fee', 'impression', 'click', 'ctr', 'acp', 'cpm', 'like', 'comment',
                                 'collect', 'follow', 'share', 'interaction', 'cpi', 'actionButtonClick',
                                 'actionButtonCtr', 'screenshot', 'picSave', 'reservePV', 'searchCmtClick',
                                 'searchCmtClickCvr', 'searchCmtAfterReadAvg', 'searchCmtAfterRead', 'wordAvgLocation',
                                 'wordImpressionRankFirst', 'wordImpressionRateFirst', 'wordImpressionRankThird',
                                 'wordImpressionRateThird', 'wordClickRankFirst', 'wordClickRateFirst',
                                 'wordClickRankThird', 'wordClickRateThird', "rgmv"], 'splitColumns': [],
                     'needTotal': True,
                     'needList': True, 'needSize': True, 'timeUnit': 'DAY', 'pageSize': 200, 'sorts': [],
                     'reportType': 'NOTE', 'startDate': '2024-01-01', 'endDate': '2024-01-31',
                     'filters': [{'column': 'marketingTarget', 'operator': 'in', 'values': ['3']}]}
        is_success, res = apis.XhsSpider().get_xhs_put_cost(payload=json_data, account='base')
        print(res)

    def test_get_goods_note_detail(self):
        params = {
            'dateType': '0',
            'startDate': '2024-04-03',
            'endDate': '2024-04-06',
            'orderField': 'readNum',
            # 'firstCategoryId': '',
            # 'secondCategoryId': '',
            'noteDataType': '1',
            'pageNo': '1',
            'pageSize': '100',
            # 'noteFilterValue': '',
        }
        is_success, res = apis.XhsSpider().get_goods_note_detail(param=params)
        print(res)

    def test_get_keyword_recommend(self):
        payload = {"attributeList": ["9d07d06bafc245a490cfbea2874af9bfcaizhi", "9d07d06bafc245a490cfbea2874af9bfweidao",
                                     "9d07d06bafc245a490cfbea2874af9bfkougan",
                                     "9d07d06bafc245a490cfbea2874af9bfbaozhuang",
                                     "9d07d06bafc245a490cfbea2874af9bfnianlingjieduan",
                                     "9d07d06bafc245a490cfbea2874af9bfgongxiao/gongneng",
                                     "9d07d06bafc245a490cfbea2874af9bfchangjing",
                                     "9d07d06bafc245a490cfbea2874af9bfqita", "9d07d06bafc245a490cfbea2874af9bfjiage",
                                     "9d07d06bafc245a490cfbea2874af9bfyanse",
                                     "9d07d06bafc245a490cfbea2874af9bfchengfen",
                                     "9d07d06bafc245a490cfbea2874af9bfrenqun"],
                   "taxonomyId": "9d07d06bafc245a490cfbea2874af9bf", "requestType": "industry",
                    "priceUplift": 0,
                   "attributeNameList": ["材质", "味道", "口感", "包装", "年龄阶段", "功效/功能", "场景", "其他",
                                         "价格", "颜色", "成分", "人群"]}
        is_success, res = apis.XhsSpider().get_keyword_recommend(payload=payload)
        print(res.get("data").get("wordNum"))
        # print(res)

    def test_get_taxonomy(self):
        param = {"taxonomyId": "133bb95d54f64bce991f953fd7211653"}
        is_success, res = apis.XhsSpider().get_taxonomy_id(param=param)
        print(res)

    def test_get_fans_data(self):
        payload = {
            "requestBody": {
                "blockElements": [
                    {"blockKey": "fansInfoProCenterTrend", "filterMap": {"dateType": 3}}
                ]
            }
        }
        is_success, res = apis.XhsSpider().get_fans_data(payload=payload)
        print(res)
        
    def test_get_search_word_daily_data(self):
        param = {
            "searchWord": "宝宝馋了果泥"
        }
        is_success, res = apis.XhsSpider().get_search_word_daily_data(param=param)
        print(res)
