import datetime
import json
import traceback

from xhs_data.database import database
from xhs_data.database.models import DasDingtalkProcessInstance, DasDingtalkProcessTask,\
    DasDingtalkPurchaseOrderOaRecord, DasDingtalkPurchaseProductInfo

db = database.create_app()


def get_instance_by_id(instance_id):
    process_instance_items = DasDingtalkProcessInstance.list(db=db, instance_id=instance_id)
    return process_instance_items


def record_task(process_instance_items):
    for process_instance_item in process_instance_items:
        process_code = process_instance_item.process_code
        process_instance_id = process_instance_item.process_instance_id
        
        form_items = {}
        for form_component in json.loads(process_instance_item.form_component_values):
            if form_component.get("name"):
                form_items[form_component.get("name")] = form_component.get("value")
            else:
                index = 0
                auto_key = f"unnamed{index}"
                while form_items.get(auto_key):
                    index += 1
                    auto_key = f"unnamed{index}"
                form_items[auto_key] = form_component.get("value")
        for task in json.loads(process_instance_item.tasks):
            # print(task)
            task_id = task["task_id"]
            if task.get("create_time"):
                create_time = task.get("create_time")
                task["create_time"] = datetime.datetime.strptime(create_time, "%Y-%m-%dT%H:%MZ")
            if task.get("finish_time"):
                finish_time = task.get("finish_time")
                task["finish_time"] = datetime.datetime.strptime(finish_time, "%Y-%m-%dT%H:%MZ")
            process_record_item = DasDingtalkProcessTask.get(db, process_code, str(task_id))
            if process_record_item:
                process_record_item.activity_id = task.get("activity_id")
                process_record_item.start_time = task.get("create_time")
                process_record_item.finish_time = task.get("finish_time")
                process_record_item.processor_user_id = task.get("user_id")
                process_record_item.result = task.get("result")
                process_record_item.status = task.get("status")
            else:
                process_record_item = DasDingtalkProcessTask(
                    process_code=process_code,
                    process_instance_id=process_instance_id,
                    task_id=str(task_id),
                    activity_id=task.get("activity_id"),
                    start_time=task.get("create_time"),
                    finish_time=task.get("finish_time"),
                    processor_user_id=task.get("user_id"),
                    result=task.get("result"),
                    status=task.get("status"),
                    form_items=json.dumps(form_items, ensure_ascii=False),
                    instance_remark="手动添加",
                    instance_result='agree'
                )
                DasDingtalkProcessTask.insert(db, process_record_item)
        db.commit()

             
def record_purchase_order_oa(process_instance_items):

    for process_instance_item in process_instance_items:
        process_instance_id = process_instance_item.process_instance_id
        instance_status = process_instance_item.status
        instance_result = process_instance_item.result
        record_item = DasDingtalkPurchaseOrderOaRecord.get(db, process_instance_id=process_instance_id)
        if record_item:
            form_items = record_item.form_items
            try:
                form_items = json.loads(form_items)
            except Exception as e:
                print(process_instance_id)
                continue
            product_code_list = []
            # 69码提出来,分割
            product_text = json.loads(form_items.get("商品信息"))
            for i in product_text:
                insert_dict = {item['label']: item['value'] for item in i["rowValue"]}
                if insert_dict.get("69码") not in product_code_list:
                    product_code_list.append(insert_dict.get("69码"))
            product_codes = ",".join(product_code_list)
            record_item.product_codes = product_codes
            record_item.delivery_date = form_items.get("下单时间")
            # record_item.instance_status = instance_status
            # record_item.instance_result = instance_result
        else:
            print("record_item is None, process_instance_id: ", process_instance_id)
            return False
    
    db.commit()


def vendor_product():
    product_info = {}
    vendor_info = {}
    record_items = DasDingtalkPurchaseOrderOaRecord.list(db)
    for record_item in record_items:
        vendor_name = record_item.vendor_name
        product_codes = record_item.product_codes
        break


if __name__ == '__main__':
    process_code = "PROC-292A3FF6-9C12-4B08-ACC0-84B021C2841E"
    # process_instance_items = get_instance_by_id(process_code)
    # process_instance_items = [process_instance_items[0]]
    # record_task(process_instance_items)
    # record_purchase_order_oa(process_instance_items)
    vendor_product()