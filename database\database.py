import pymysql
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from utils.encryption import AesEncryption
from xhs_data.settings import config
from urllib import parse
import redis

pymysql.install_as_MySQLdb()


def init_engine():
    username = config.get("mysql").get("username")
    password = parse.quote_plus(AesEncryption(config.get("secret_key")).decryption(config.get("mysql").get("password")))
    host = config.get("mysql").get("host")
    port = config.get("mysql").get("port")
    database = config.get("mysql").get("database")
    charset = config.get("mysql").get("charset")
    engine = create_engine(
        "mysql+pymysql://%s:%s@%s:%s/%s?charset=%s" % (username, password, host, port, database, charset),
        max_overflow=0,
        pool_size=5,
        pool_timeout=30,
        pool_recycle=-1,
        # echo=True
    )
    return engine


def init_redis():
    host = config.get("redis").get("host")
    port = config.get("redis").get("port")
    db = config.get("redis").get("db")
    password = AesEncryption(config.get("secret_key")).decryption(config.get("redis").get("password"))
    pool = redis.ConnectionPool(host=host, port=port, db=db, password=password)
    r = redis.Redis(connection_pool=pool)
    return r


def create_app():
    engine = init_engine()
    return sessionmaker(bind=engine)()


db = create_app()
