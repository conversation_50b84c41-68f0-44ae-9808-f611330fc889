# encoding=utf-8
"""
小红书-商品详情
"""
import datetime
from xhs_apis.apis import <PERSON>hs<PERSON><PERSON><PERSON>
from utils.dingtalk import DingTalk
from xhs_data.settings import config
from utils.encryption import AesEncryption
from utils.logx import logger
from xhs_data.database import database
from xhs_data.database.models import DasApplicationXhsGoodsDetail
from xhs_data.database.database import create_app
from xhs_data.utils.get_time import get_time

db = database.create_app()


def extract_value(row, key, default=None):
    return row.get(key, {}).get("value", default)


def process_note(row):
    return {"goods_note_pay_money": extract_value(row, "payGmv", "0")}


def process_live(row):
    return {"goods_live_pay_money": extract_value(row, "payGmv", "0")}


def process_card(row):
    return {"goods_card_pay_money": extract_value(row, "payGmv", "0")}


def process_all(row):
    return {
        "goods_id": extract_value(row, 'itemBasic')[0].get("itemId"),
        "goods_name": extract_value(row, 'itemBasic')[0].get("itemName"),
        "pay_money": extract_value(row, "payGmv"),
        "goods_uv": extract_value(row, "goodsUv"),
        "cart_user_num": extract_value(row, "cartUserNum"),
        "pay_user_num": extract_value(row, "payUserNum"),
        "entrance_channel": extract_value(row, "entranceChannel")
    }


processing_functions = {
    '笔记': process_note,
    '直播': process_live,
    '商卡': process_card,
    'all': process_all
}


class XhsGoodsDetail:
    def __init__(self, time_type="昨天", item_id_str=None):
        self.spider_at = datetime.datetime.now()
        args = {"time_type": time_type, "timestamp_type": "秒", "time_format": "%Y-%m-%d"}
        get_time(args)
        self.time_tuple = args['time_tuple']
        self.item_ids = item_id_str.split(',')
        logger.info('XhsGoodsDetail-获取时间为:', self.time_tuple)

        # self.ding_talk = DingTalk(webhook=config.get("ding_talk").get("webhook"),
        #                           secret=AesEncryption(config.get("secret_key")).decryption(
        #                               (config.get("ding_talk").get("secret")))
        #                           )

    def get_xhs_goods_detail(self):
        # 获取小红书商品详情
        start_time = self.time_tuple[0]
        end_time = self.time_tuple[1]

        payload = {
            "requestBody": {"blockElements": [
                {
                    'filterMap': {
                        'dateType': 0,
                        'startDate': start_time,
                        'endDate': end_time,
                    },
                    'page': 1,
                    'size': 100,
                    'blockKey': 'sellerCarrierItemDataList',
                }
            ]}
        }
        for item_id in self.item_ids:
            payload['requestBody']['blockElements'][0]['filterMap']['itemId'] = item_id
            self.request_xhs_goods_detail(payload=payload)
            logger.info('XhsGoodsDetail-获取小红书商品详情成功:', item_id)

    def request_xhs_goods_detail(self, payload):
        is_success, data = XhsSpider().get_seller_carrier_item(payload=payload)
        if not is_success:
            return False
        res_data = data.get('data')

        if not res_data:
            return False
        for row in res_data:
            block_name = row.get("blockName")
            if block_name == "商家商品载体数据列表":
                data_dict = self.dispose_item_data_list(row)
                data_dict['start_date'] = datetime.datetime.strptime(self.time_tuple[0], '%Y-%m-%d')
                data_dict['end_date'] = datetime.datetime.strptime(self.time_tuple[1], '%Y-%m-%d') + datetime.timedelta(
                    hours=23, minutes=59, seconds=59)
                keys = [key for key in data_dict]
                DasApplicationXhsGoodsDetail.bulk_insert_on_duplicate(db=db, data=data_dict, update_keys=keys)
                logger.info(data_dict)

    @staticmethod
    def dispose_item_data_list(data):
        rows = data.get("data", "")
        data_dict = {}
        for row in rows:
            entrance_channel = row.get("entranceChannel", {}).get("value", "")
            if entrance_channel == '全部':
                new_carrier_name= row.get("newCarrierName", {}).get("value", "")
                if new_carrier_name !='all':
                    continue
                new_carrier_name_group = row.get("newCarrierNameGroup", {}).get("value", "")

                if new_carrier_name_group in processing_functions:
                    data_dict.update(processing_functions[new_carrier_name_group](row))
        return data_dict

    def run(self):
        self.get_xhs_goods_detail()


if __name__ == '__main__':
    time_type = "昨天"
    # item_id_str = '63ecbb6ebc9ab20001c99781'
    item_id_str = '6433bd2085314f00012c413e'
    XhsGoodsDetail(time_type, item_id_str).run()
