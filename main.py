import sys
import __init__
from utils.logx import logger
from xhs_data.xhs_fans_data import XhsFansData
from xhs_put_cost import XhsPutCost
from xhs_goods_detail import XhsGoodsDetail
from xhs_goods_note_detail import XhsGoodsNoteDetail
from xhs_key_search import XhsKeySearch
from xhs_grass_articles import XhsGrassArticles

# python .\main.py {环境} {task} {time_type}

if __name__ == '__main__':
    task = sys.argv[2]
    if task == 'xhs_put_cost':
        time_type = sys.argv[3]
        xhs_put_cost = XhsPutCost(time_type=time_type)
        xhs_put_cost.run()
    elif task == 'xhs_goods_detail':
        """投流费用"""
        time_type = sys.argv[3]
        item_id_str = sys.argv[4]
        xhs_goods_detail = XhsGoodsDetail(time_type=time_type, item_id_str=item_id_str)
        xhs_goods_detail.run()
    elif task == 'xhs_goods_note_detail':
        """官方笔记数据详情"""
        time_type = sys.argv[3]
        tool = sys.argv[4]  # 全部笔记/商品笔记
        xhs_goods_note_detail = XhsGoodsNoteDetail(time_type=time_type, tool=tool)
        xhs_goods_note_detail.get_xhs_goods_note_detail()
    elif task == 'xhs_key_search':
        # 小红书-关键词检索
        XhsKeySearch().get_xhs_key_search()
    elif task == 'xhs_key_search_word':
        # 小红书-关键词行业推词
        XhsKeySearch().get_xhs_spu_search()
    elif task == 'xhs_grass_articles':
        # 小红书种草文章
        XhsGrassArticles().get_xhs_grass_articles()
    elif task == 'xhs_fans_data':
        # 小红书粉丝数据
        XhsFansData().get_xhs_fans_data()
    elif task == 'xhs_site_search_word':
        # 小红书-站内搜索词
        XhsKeySearch().get_site_search()
    else:
        logger.error(f'小红书_任务名称错误{task}')
