import os
import sys
import json

def init_config():
    argv = sys.argv
    mode = str
    if len(argv) > 1:
        mode = argv[1]
    else:
        mode = ""

    if mode == 'pro':
        return json.load(open(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'pro.json'), encoding='utf8'))
    elif mode == 'test':
        return json.load(open(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'test.json'), encoding='utf8'))
    else:
        return json.load(open(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'dev.json'), encoding='utf8'))


_config = init_config()


def get(key):
    return _config.get(key)
