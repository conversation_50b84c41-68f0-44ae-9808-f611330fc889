# encoding=utf-8
"""
小红书-关键词检索
"""
import time

import pandas as pd
from datetime import datetime, timedelta, date

import requests
from xhs_apis.apis import XhsSpider
from utils.logx import logger
from collections import Counter
from xhs_data.database.database import create_app
from xhs_data.database.database import init_engine
from utils.dingtalk import DingTalk
from xhs_data.database.models import XhsKeyWord, DasApplicationXhsSiteKeyword
from xhs_data.settings import config
from utils.encryption import AesEncryption


# keyword_list = ['英氏', '秋田满满', '小皮', '爷爷的农场', '小鹿蓝蓝', '光合星球', 'babycare', '窝小芽',
#                 '宝宝馋了', '奶酪博士', '碧欧奇', '嘉宝', '禾泱泱', '喵小侠', '亨氏', '未零', '不二宝贝', '哆啦大自然',
#                 '法优乐', '怡芽', '冲冲粥', '冲冲面', '宝宝酸奶', '婴儿酸奶', '儿童酸奶', '秋田满满米粉', '英氏米粉',
#                 '小皮米粉', '爷爷的农场米粉', '嘉宝米粉', '宝宝馋了冲冲面', '宝宝馋了冲冲粥', '冲冲面', '冲冲粥',
#                 '哆啦大自然即食粥', '宝宝馋了酸奶', '宝宝馋了婴标酸奶', '婴标酸奶', '简爱酸奶', '卡士酸奶', '乐纯酸奶',
#                 '宝宝馋了果泥', '英氏果泥', '小鹿蓝蓝果泥', '小皮果泥', '爷爷的农场果泥', '亨氏果泥', 'bebebus婴儿床',
#                 '可心柔', '贝易餐椅', '大嘴鸭泡奶机', '哈卡达餐椅', '小雅象奶瓶', '新贝吸奶器', '贝易床围栏',
#                 '皇宠辅食碗', '皇宠奶瓶刷', '皇宠好吸杯', '小象脚丫洗衣液', 'bebecook辅食粥', 'inne钙镁锌',
#                 '贝德美洗发水', '舔鲜鹅肝']
# taxonomy_item = {
#     "磨牙饼干/磨牙棒": "133bb95d54f64bce991f953fd7211653",
#     "米粉": "9d07d06bafc245a490cfbea2874af9bf"
# }


def get_keyword():
    keyword_list = []
    taxonomy_item = {}
    url = "http://gripper.aizinger.com:8889/api/dingdoc/business_data/237"
    # url = "http://127.0.0.1:8889/api/dingdoc/business_data/344"

    payload_1 = {
        "select_params": {
            "channel": "小红书",
            "is_it_enabled": "是"
        }
    }
    response_1 = requests.request("GET", url, json=payload_1).json()
    payload_2 = {
        "select_params": {
            "channel": "全部",
            "is_it_enabled": "是"
        }
    }
    response_2 = requests.request("GET", url, json=payload_2).json()

    data_1 = response_1.get("data")
    data_2 = response_2.get("data")
    for row in data_1:
        search_term_type = row.get("search_term_type")
        if search_term_type == '行业推词':
            taxonomy_item[row.get("search_terms")] = row.get("parameter")
            continue
        key_word = row.get("search_terms")
        keyword_list.append(key_word)
    for row in data_2:
        key_word = row.get("search_terms")
        keyword_list.append(key_word)

    return keyword_list, taxonomy_item


keyword_list, taxonomy_item = get_keyword()
print(keyword_list)
print(taxonomy_item)
db = create_app()


class XhsKeySearch:
    def __init__(self, keyword=None):
        if keyword is None:
            self.keyword = keyword_list
        else:
            self.keyword = keyword.split(',')
        self.spider_at = datetime.now()
        logger.info('XhsKeySearch-获取时间为:', self.spider_at)
        self.dataframe = pd.DataFrame()
        self.ding_talk = DingTalk(webhook=config.get("ding_talk").get("webhook"),
                                  secret=AesEncryption(config.get("secret_key")).decryption(
                                      (config.get("ding_talk").get("secret"))))

    def get_xhs_key_search(self):
        for key_word in self.keyword:
            payload = {
                "requestType": "search",
                "priceUplift": 0,
                "keyword": key_word,
                "highEffectRec": 0
            }
            self.request_xhs_key_search(search_key_word=key_word, payload=payload)
        try:
            tool = "以词推词"
            self.dataframe['tool'] = tool
            now = date.today()
            to_day = datetime(now.year, now.month, now.day)
            to_day_str = to_day.strftime('%Y-%m-%d')
            select_gt_le_info_data = XhsKeyWord.select_gt_le_info(db, to_day=to_day_str,
                                                                  search_keyword=self.keyword,
                                                                  tool=tool)
            if select_gt_le_info_data:
                data = {"deleted_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                XhsKeyWord.update(db, to_day=to_day, search_keyword=self.keyword, data=data, tool=tool)

            self.dataframe.to_sql(XhsKeyWord.__tablename__, init_engine(), if_exists='append', index=False)
            db.commit()
            logger.info("XhsKeySearch-入库成功_以词推词:", self.spider_at)
        except Exception as e:
            db.rollback()
            logger.error("XhsKeySearch-入库失败_以词推词:", e)
            print()

    def request_xhs_key_search(self, search_key_word, payload):
        logger.info('XhsKeySearch-请求参数为:', search_key_word)
        is_success, data = XhsSpider().get_keyword_search(payload=payload)
        if not is_success:
            return False
        res_data = data.get('data')
        rows = res_data.get('wordList', None)
        if rows is None:
            logger.info('XhsKeySearch-请求参数为:', search_key_word, '无数据')
            return
        insert_data = self.dispose_data(search_key_word, rows)
        self.dataframe = self.dataframe.append(pd.DataFrame(insert_data))
        time.sleep(0.5)

    def dispose_data(self, search_key_word, rows):
        insert_data = []
        for row in rows:
            insert_item = {
                "spider_at": self.spider_at,  # 爬取时间
                "search_keyword": search_key_word,  # 搜索关键词
                "keyword": row.get('keyword'),  # 关键词
                "recommend_reason": "|".join(row.get('recommendReason', [])),  # 推荐理由
                "competition_level": row.get('competitionLevel'),  # 竞争指数
                "month_pv": row.get('monthpv'),  # 月均搜索指数
                "bid": row.get('bid') / 100,  # 报价
                "source": row.get('source'),  # 来源
                "competition_score": row.get('competitionScore'),  # 竞争力
                "soc": row.get('soc'),  # 搜索指数
            }
            insert_data.append(insert_item)
        return insert_data

    def get_xhs_spu_search(self):
        for key_word, taxonomy_id in taxonomy_item.items():
            param = {"taxonomyId": "133bb95d54f64bce991f953fd7211653"}
            is_success, data = XhsSpider().get_taxonomy_id(param)
            if not is_success:
                logger.error('XhsKeySearch-获取taxonomyId失败:', key_word)
                continue
            res_data = data.get('data')
            attribute_list = []
            attribute_name_list = []
            for res in res_data:
                attribute_list.append(res.get('taxonomyId'))
                attribute_name_list.append(res.get('taxonomyName'))
            payload = {
                "attributeList": attribute_list,
                "taxonomyId": taxonomy_id,
                "requestType": "industry",
                "priceUplift": 0,
                "attributeNameList": attribute_name_list
            }
            self.request_spu_search(search_key_word=key_word, payload=payload)

        try:
            tool = "行业推词"
            search_keyword = list(taxonomy_item.keys())
            self.dataframe['tool'] = tool
            now = date.today()
            to_day = datetime(now.year, now.month, now.day)
            to_day_str = to_day.strftime('%Y-%m-%d')
            select_gt_le_info_data = XhsKeyWord.select_gt_le_info(db, to_day=to_day_str, search_keyword=search_keyword,
                                                                  tool=tool)
            if select_gt_le_info_data:
                data = {"deleted_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                XhsKeyWord.update(db, to_day=to_day, search_keyword=search_keyword, data=data, tool=tool)

            self.dataframe.to_sql(XhsKeyWord.__tablename__, init_engine(), if_exists='append', index=False)
            db.commit()
            logger.info("XhsKeySearch-入库成功_行业推词:", self.spider_at)
        except Exception as e:
            db.rollback()
            logger.error("XhsKeySearch-入库失败_行业推词:", e)
            print()

    def request_spu_search(self, search_key_word, payload):
        logger.info('XhsKeySearch_spu_search-请求参数为:', search_key_word)
        is_success, data = XhsSpider().get_keyword_recommend(payload=payload)
        if not is_success:
            return False
        res_data = data.get('data')
        rows = res_data.get('wordList', None)
        if rows is None:
            logger.info('XhsKeySearch_spu_search-请求参数为:', search_key_word, '无数据')
            return
        insert_data = self.dispose_data(search_key_word, rows)
        self.dataframe = self.dataframe.append(pd.DataFrame(insert_data))
        time.sleep(0.5)

    def get_site_search(self):
        for key_word in self.keyword:
            param = {
                "searchWord": key_word
            }
            is_success, data = XhsSpider().get_search_word_daily_data(param=param)
            if not is_success:
                return False
            rows = data.get('data', {}).get('dailyData')
            if rows is None:
                logger.info('XhsKeySearch_get_site_search-请求参数为:', key_word, '无数据')
                return
            insert_data = []
            for row in rows:
                insert_item = {
                    "search_keyword": key_word,  # 搜索关键词
                    "date_key": row.get('dateKey'),  # 日期
                    "search_score": row.get('searchScore'),  # 搜索指数
                    "imp_num": row.get('impNum'),  # 关联曝光量
                    "read_num": row.get('readNum'),  # 关联阅读量
                    "engage_num": row.get('engageNum'),  # 关联互动量
                    "node_num": row.get('noteNum'),  # 关联笔记数
                    "user_num": row.get('userNum'),  # 关联作者量
                }
                insert_data.append(insert_item)
            need_fields = ['search_score', 'imp_num', 'read_num',
                           'engage_num', 'node_num', 'user_num']
            DasApplicationXhsSiteKeyword.bulk_insert_on_duplicate(db=db, data=insert_data, update_keys=need_fields)
            logger.info('XhsKeySearch_get_site_search-入库成功:', key_word)
            time.sleep(0.5)
        logger.info('XhsKeySearch_get_site_search-获取完成')
        return True


if __name__ == '__main__':
    XhsKeySearch().get_xhs_key_search()
    # XhsKeySearch().get_xhs_spu_search()
