"""
    xhs自制sdk
"""
import __init__
import os
import json
import time
import requests
import platform
import urllib3
from typing import Tuple

from xhs_data.settings import config
from utils.encryption import AesEncryption
from utils.logx import logger
from utils.common import retry
from DrissionPage import WebPage, ChromiumOptions


urllib3.disable_warnings()
AD_PROXIES_ACCOUNT = {
    # """聚光代理账号信息"""
    "ad_569822": {"vSellerId": "6524b4d07fa15200013b87b4"},  # 杭州昕然广告创意有限公司
    "ad_167331": {"vSellerId": "6348e820b74d0a00017cfbe9"},  # 江苏拾光宝盒信息技术有限公司
    "ad_106235": {"vSellerId": "621f3e170954bf00018271a5"},  # 武汉卓尔数字传媒科技有限公司
    "ad_45247": {"vSellerId": "5fbf8cbb891a440001d4571d"},  # 乐推（上海）文化传播有限公司
    "ad_866725": {"vSellerId": "6604e05f7455790001a70a57"},  # 武汉微思敦网络技术有限公司-酸奶
    "ad_861061": {"vSellerId": "6602447ef0b53500013ba010"},  # 武汉微思敦网络技术有限公司
    # 由于小红书接口限制,修改为vSellerId登录
    "ad_1798043": {"vSellerId": "669f57debd86ee00153112e1"},  # 上海小花猫文化传播有限公司
    "ad_1817413": {"vSellerId": "66a1f047dcab6c00153660f8"},  # 日弘传媒（上海）有限公司
    "ad_1896481": {"vSellerId": "66ab4c3ffaac8e001536c223"},  # 厦门龙赢网络技术服务有限公司
    "ad_6101018": {"vSellerId": "675fcebe11448c00159d4977"},  # 深圳市东信时代信息技术有限公司
    "ad_7839087": {"vSellerId": "6864ae7e832e500015702d45"},  # 杭州众行壹策网络科技有限公司
    "ad_7840116": {"vSellerId": "6864c682294d990015903c7d"},  # 杭州众行壹策网络科技有限公司
    "ad_7837421": {"vSellerId": "6864c6d618460a0015abfedc"},  # 杭州众行壹策网络科技有限公司
    "ad_7403271": {"vSellerId": "680882d4aeb07800155ff1a3"},  # 厦门龙赢网络技术服务有限公司
}


class XhsSpider:

    def __init__(self):
        self.cookies = self.get_cookies()
        self.account = config.get("login").get("account")
        self.password = AesEncryption(config.get("secret_key")).decryption(config.get("login").get("password"))
        self.ad_account = config.get("ad_login").get("account")
        self.ad_password = AesEncryption(config.get("secret_key")).decryption(config.get("ad_login").get("password"))
        self.agent_account = config.get("agent_login").get("account")  # 炬辰
        self.agent_password = AesEncryption(config.get("secret_key")).decryption(config.get("agent_login").get("password"))

    @staticmethod
    def get_cookies():
        cookie_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "xhs_cookies.json")
        if not os.path.exists(cookie_file_path):
            return {}
        with open(cookie_file_path, 'r', encoding='utf8') as fr:
            cookies = json.load(fr)
        return cookies

    @staticmethod
    def save_cookies(cookies):
        cookie_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "xhs_cookies.json")
        with open(cookie_file_path, 'w+', encoding='utf8') as fw:
            json.dump(cookies, fw)

    # 处理cookie解析
    @staticmethod
    def deal_with_cookie(primitive_cookies):
        now_cookies = {}
        for cookie in primitive_cookies:
            now_cookies[cookie.name] = cookie.value
        # 将dict的cookies转换成字符串
        cookies = ';'.join([str(key) + '=' + str(value) for key, value in now_cookies.items()])
        return cookies

    @staticmethod
    def pgy_login(account: str, password: str) -> str:
        if platform.system().lower() == "windows":
            co = ChromiumOptions()
            # co.headless()  # 无头模式
            # co.incognito(True)
            # co.set_argument('--no-sandbox')  # 无沙盒模式
            # co.set_argument('--guest')  # 游客模式
        else:
            co = ChromiumOptions().set_paths(browser_path=r'/opt/google/chrome/google-chrome')  # linux系统路径
            # co.incognito()  # 匿名模式
            # co.headless()  # 无头模式
            co.set_argument('--no-sandbox')  # 无沙盒模式
            co.auto_port()
        page = WebPage(chromium_options=co)
        page.get("https://pgy.xiaohongshu.com/")
        page.wait(5)
        is_checked = page.ele("x://button[text()='登录']", timeout=5)
        if not is_checked:
            print("账号无需登录")
            cookies = page.cookies(as_dict=True)
            cookies_str = ''
            for key, value in cookies.items():
                cookies_str += f"{key}={value}; "
            return cookies_str
        print("账号需要登录")
        page.ele("x://button[text()='登录']").click()
        page.wait(5)
        page.ele("x://div[text()='账号登录']").click()
        time.sleep(1)
        page.ele("x://input[@name='email']").input(account, clear=True)
        page.ele("x://input[@name='password']").input(password, clear=True)
        page.ele("x://span[@class='btn-content']").click()
        page.wait(10)
        print(page.url)
        is_checked = page.ele("x://button[text()='登录']", timeout=5)
        if not is_checked:
            print("登录成功")
            cookies = page.cookies(as_dict=True)
            cookies_str = ''
            for key, value in cookies.items():
                cookies_str += f"{key}={value}; "
            page.close()
        else:
            print("登录失败")
            page.close()
            raise Exception("登录失败")
        
        return cookies_str
    
    def login(self):
        """主号聚光登录、蒲公英登录"""
        # 蒲公英登录
        primitive_cookies = self.pgy_login(self.account, self.password)
        # 小红书聚光接口登录获取cookie
        self.ad_proxies_login("base")
        
        self.cookies['cookie'] = primitive_cookies
        self.save_cookies(self.cookies)
        logger.info(f"XHS_login_success")

    def ad_proxies_login(self, proxies_account=None):
        """小红书聚光接口登录代理账号获取cookie"""
        if proxies_account.startswith("ad_juchen"):
            account = self.agent_account
            password = self.agent_password
            ad_juchen_base = self.ad_login(account, password)
            self.cookies[proxies_account] = ad_juchen_base
            self.save_cookies(self.cookies)
        else:
            account = self.ad_account
            password = self.ad_password
        base_ad_cookie = self.cookies.get('base_ad_cookie', '')
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) '
                          'Chrome/111.0.0.0 Safari/537.36 Edg/111.0.1661.62',
            "Origin": "https://ad.xiaohongshu.com",
            "Referer": "https://ad.xiaohongshu.com",
            "cookie": base_ad_cookie,
        }
        res = requests.get("https://ad.xiaohongshu.com/api/leona/user/info", headers=headers)
        if "请先登录" in res.text or res.status_code != 200:
            print("请先登录")
            base_ad_cookie = self.ad_login(account, password)
            self.cookies['base_ad_cookie'] = base_ad_cookie
            self.save_cookies(self.cookies)
        else:
            print("已登录")
        if proxies_account == "base":
            return
        session = requests.session()
        # 代理账号进行登录获取cookie
        proxies_json_data = AD_PROXIES_ACCOUNT.get(proxies_account)
        if not proxies_json_data:
            logger.info(f"proxies_account:{proxies_account}不存在")
            return
        proxies_response = session.post("https://ad.xiaohongshu.com/api/leona/brand/shadow_login",
                                        headers=headers,
                                        json=proxies_json_data)
        logger.info(f"proxies_account:{proxies_account},proxies_response:{proxies_response.text}")
        ad_cookies_ticket = self.deal_with_cookie(proxies_response.cookies)
        self.cookies[proxies_account] = ad_cookies_ticket + ";"+ base_ad_cookie
        self.save_cookies(self.cookies)

    def ark_login(self):
        """小红书千帆登录"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) '
                          'Chrome/111.0.0.0 Safari/537.36 Edg/111.0.1661.62',
            'Content-Type': 'application/json;charset=UTF-8', 'Accept': '*/*',
            'origin': 'https://customer.xiaohongshu.com',
            'referer': 'https://customer.xiaohongshu.com/login?service=https://ark.xiaohongshu.com/'
                       'app-datacenter/good-data/detail/63ecbb6ebc9ab20001c99781/goods/holder?'
                       'visual=spu&dateType=1&date=2024-01-28',

        }
        json_data = {"account": '<EMAIL>',
        # json_data = {"account": self.account,
                     # "password": self.password,
                     "password": 'Bbcl123456',
                     'service': 'https%3A%2F%2Fark.xiaohongshu.com%2Fapp-datacenter%2Fgood-data%2Fdetail%2F63ecbb6ebc9a'
                                'b20001c99781%2Fgoods%2Fholder%3Fvisual%3Dspu',
                     }
        session = requests.session()
        response_login_ark = session.post('https://customer.xiaohongshu.com/api/cas/loginWithAccount',
                                          headers=headers,
                                          json=json_data,
                                          )
        login_json = response_login_ark.json()
        ticket = login_json.get('data')
        headers['origin'] = 'https://ark.xiaohongshu.com'
        headers['referer'] = 'https://ark.xiaohongshu.com/app-datacenter/good-data/detail/63ecbb6ebc9ab20001c99781/' \
                             'goods/holder?visual=spu&ticket=ST-6bf383cff1458e42107b518dd017c2e0'
        json_data = {
            'system': 'https%3A%2F%2Fark.xiaohongshu.com%2Fapp-datacenter%2Fgood-data%2Fdetail%'
                      '2F63ecbb6ebc9ab20001c99781%2Fgoods%2Fholder%3Fvisual%3Dspu',
            'ticket': ticket,
        }
        ark_response_ticket = session.post('https://ark.xiaohongshu.com/api/edith/open/ssologin',
                                           headers=headers, json=json_data).json()
        access_token = ark_response_ticket.get('data', {}).get('access_token')
        self.cookies['qf_access_token'] = access_token
        self.save_cookies(self.cookies)

    def call_requests(self, method: str, url: str, params: dict = None, payload: dict = None, account: str = 'base'):
        """
        重写请求
        @method:"POST"或"GET"
        @url:链接
        @params:请求参数
        @return:
        """
        data = dict()
        for i in range(3):
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36 Edg/111.0.1661.62',
                'Content-Type': 'application/json;charset=UTF-8', 'Accept': '*/*',
                'referer': 'https://pgy.xiaohongshu.com/',
                'origin': 'https://pgy.xiaohongshu.com',
            }
            if account == 'ark':
                headers['authorization'] = self.cookies.get('qf_access_token', '')
            elif account == 'base':
                headers['cookie'] = self.cookies.get('cookie', '') + self.cookies.get('base_ad_cookie', '')
            elif account.startswith("ad_"):
                headers['cookie'] = self.cookies.get(account, '')
                headers['origin'] = "https://ad.xiaohongshu.com"
                headers['referer'] = ""
                
            session = requests.session()
            req = None
            try:
                if method == "GET":
                    req = session.request(method="GET", url=url, headers=headers, params=params, timeout=60)
                elif method == "POST":
                    req = session.request(method="POST", url=url, headers=headers, json=payload, timeout=60)
            except Exception as e:
                logger.error(f"xhs get {url} req error, payload:{payload},error message is {e}")
                if i < 2:
                    continue
                else:
                    raise Exception(f"XHSSpider get {url} req error, error message is {e}")
            if req.status_code != 200:
                logger.warning(f"xhs get {url} warning，error message is {data}, index is {i + 1}")
                if account == 'ark':
                    self.ark_login()
                elif account == 'base':
                    self.login()
                elif account.startswith("ad_"):
                    self.ad_proxies_login(account)
                else:
                    raise Exception(f"XHSSpider get {url} req error,输入account {account} error")
                self.cookies = self.get_cookies()
                continue
            data = req.json()
            success = data.get('success')

            if success:
                logger.info(f"xhs get {url} success,index is {i + 1}")
                break
                # 接口调用失败，重新获取cookie
            if i < 2:
                logger.warning(f"xhs get {url} warning，error message is {data}, index is {i + 1}")
                if account == 'ark':
                    self.ark_login()
                elif account == 'base':
                    self.login()
                elif account.startswith("ad_"):
                    self.ad_proxies_login(account)
                else:
                    raise Exception(f"XHSMaMaSpider get {url} req error,输入account {account} error")
                self.cookies = self.get_cookies()
            else:
                logger.error(f"xhs get {url}  error, error message is {data}")
                raise Exception(f"XHSSpider get {url} req error, error message is {data}")
        return data

    def get_grass_articles(self, payload: dict) -> Tuple[bool, dict]:
        """
        XHS种草文章接口
        :return:
        """
        url = 'https://pgy.xiaohongshu.com/api/solar/content/note/list'
        data = self.call_requests(method="POST", url=url, payload=payload)
        if not data:
            return False, {}
        return True, data

    def get_keyword_search(self, payload: dict) -> Tuple[bool, dict]:
        """
        XHS关键词检索接口
        :return:
        """
        url = 'https://ad.xiaohongshu.com/api/leona/rtb/tool/keyword/common/recommend'
        data = self.call_requests(method="POST", url=url, payload=payload)
        if not data:
            return False, {}
        return True, data

    def get_seller_carrier_item(self, payload: dict, params: dict = None) -> Tuple[bool, dict]:
        """
        XHS商品详情接口
        params:sellerCarrierItemDataList,sellerCarrierItemGmvOverall
        :return:
        """
        url = 'https://ark.xiaohongshu.com/api/edith/butterfly/data'
        data = self.call_requests(method="POST", url=url, params=params, payload=payload, account='ark')
        if not data:
            return False, {}
        return True, data

    def get_xhs_put_cost(self, account: str, payload: dict = None) -> Tuple[bool, dict]:
        """
        小红书投流花费
        :return:
        """
        url = 'https://ad.xiaohongshu.com/api/leona/rtb/data/report'
        data = self.call_requests(method="POST", url=url, payload=payload,account=account)
        if not data:
            return False, {}
        return True, data

    def get_goods_note_detail(self, param) -> Tuple[bool, dict]:
        """
        小红书-官方笔记数据
        """
        url = "https://pro.xiaohongshu.com/api/edith/data_center/goods_note/note/detail/list"
        data = self.call_requests(method="GET", url=url, params=param, account="base")
        if not data:
            return False, {}
        return True, data

    def get_keyword_recommend(self, payload: dict = None) -> Tuple[bool, dict]:
        """
        小红书-行业推词
        """
        url = "https://ad.xiaohongshu.com/api/leona/rtb/tool/keyword/common/recommend"
        data = self.call_requests(method="POST", url=url, payload=payload, account="ad_juchen_base")
        if not data:
            return False, {}
        return True, data
    
    def get_taxonomy_id(self, param) -> Tuple[bool, dict]:
        """
        小红书-获取分类id
        """
        url = "https://ad.xiaohongshu.com/api/leona/rtb/tool/keyword/industry/taxonomy/attribute"
        data = self.call_requests(method="GET", url=url, params=param, account="ad_juchen_base")
        if not data:
            return False, {}
        return True, data
    
    def get_fans_data(self, payload: dict) -> Tuple[bool, dict]:
        """
        小红书-粉丝数据
        """
        url = "https://pro.xiaohongshu.com/api/edith/data_center/butterfly/data"
        data = self.call_requests(method="POST", url=url, payload=payload, account="base")
        if not data:
            return False, {}
        return True, data
    
    def get_grass_articles_link(self, note_id: str) -> Tuple[bool, dict]:
        """
        XHS种草文章接口
        :return:
        """
        url = f'https://pgy.xiaohongshu.com/api/solar/note/{note_id}/detail?bizCode='
        data = self.call_requests(method="GET", url=url)
        if not data:
            return False, {}
        return True, data
    
    def get_search_word_daily_data(self, param: dict) -> Tuple[bool, dict]:
        """
        小红书-关键词全站数据
        """
        url = "https://pgy.xiaohongshu.com/api/solar/content_square/search_word_daily_data"
        data = self.call_requests(method="GET", url=url, params=param, account="base")
        if not data:
            return False, {}
        return True, data
    
    @staticmethod
    def ad_login(account: str, password: str) -> str:
        if platform.system().lower() == "windows":
            co = ChromiumOptions()
            # co.headless()  # 无头模式
            # co.incognito(True)
            # co.set_argument('--no-sandbox')  # 无沙盒模式
            # co.set_argument('--guest')  # 游客模式
        else:
            co = ChromiumOptions().set_paths(browser_path=r'/opt/google/chrome/google-chrome')  # linux系统路径
            # co.incognito()  # 匿名模式
            # co.headless()  # 无头模式
            co.set_argument('--no-sandbox')  # 无沙盒模式
            co.auto_port()
        page = WebPage(chromium_options=co)
        page.get("https://ad.xiaohongshu.com/")
        page.wait(5)
        is_checked = page.ele("x://div[text()='账号登录']", timeout=5)
        if not is_checked:
            print("账号无需登录")
            cookies = page.cookies(as_dict=True)
            cookies_str = ''
            for key, value in cookies.items():
                cookies_str += f"{key}={value}; "
            return cookies_str
        print("账号需要登录")
        page.ele("x://div[text()='账号登录']").click()
        time.sleep(1)
        page.ele("x://input[@name='email']").input(account, clear=True)
        page.ele("x://input[@name='password']").input(password, clear=True)
        page.ele("x:(//div[@class='checkbox-wrap']/div)[1]").click()
        page.ele("x://span[@class='btn-content']").click()
        page.wait(10)
        print(page.url)
        is_checked = page.ele("x://input[@name='email']", timeout=5)
        if not is_checked:
            print("登录成功")
            cookies = page.cookies(as_dict=True)
            cookies_str = ''
            for key, value in cookies.items():
                cookies_str += f"{key}={value}; "
            page.close()
        else:
            print("登录失败")
            page.close()
            raise Exception("登录失败")
        
        return cookies_str
    


if __name__ == '__main__':
    xhs = XhsSpider()
    xhs.ad_proxies_login("ad_juchen_base")
