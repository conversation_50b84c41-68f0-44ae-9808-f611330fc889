# encoding=utf-8
"""
小红书-投流费用
"""
import datetime
import time

from xhs_apis.apis import XhsSpider
from utils.dingtalk import DingTalk
from xhs_data.settings import config
from utils.encryption import AesEncryption
from utils.logx import logger
import pandas as pd
from xhs_data.database import database
from xhs_data.database.database import init_engine
from xhs_data.database.models import DasApplicationXhsPutCost
from xhs_data.utils.get_time import get_time

db = database.create_app()

AD_PROXIES_ACCOUNT = {
    # """聚光代理账号信息"""
    "宝宝馋了": {
        "account_id": "44630",
        "account": "base",
        "seller_id": "5f9cdc64ad39ce0001a6b28e",
        # "marketing_target": {"全部": ""}
        "marketing_target": {"商品推广_日常推广": "3", "产品种草": "4", "商品推广_店铺拉新": "15"}
    },  # 主号
    "昕然(宝宝馋了)": {
        "account_id": "569822",
        "account": "ad_569822",
        "seller_id": "6524b4d07fa15200013b87b4",
        "marketing_target": {"商品推广_日常推广": "3", "产品种草": "4", "商品推广_店铺拉新": "15"}
    },  # 杭州昕然广告创意有限公司
    # "拾光宝盒(宝宝馋了)": {
    #     "account_id": "167331",
    #     "account": "ad_167331",
    #     "seller_id": "6348e820b74d0a00017cfbe9",
    #     "marketing_target": {"商品推广_日常推广": "3", "产品种草": "4", "商品推广_店铺拉新": "15"}
    # },  # 江苏拾光宝盒信息技术有限公司
    # "卓尔数科(宝宝馋了)": {
    #     "account_id": "106235",
    #     "account": "ad_106235",
    #     "seller_id": "621f3e170954bf00018271a5",
    #     "marketing_target": {"商品推广_日常推广": "3", "产品种草": "4", "商品推广_店铺拉新": "15"}
    # },  # 武汉卓尔数字传媒科技有限公司
    # "乐推(宝宝馋了)": {
    #     "account_id": "45247",
    #     "account": "ad_45247",
    #     "seller_id": "9b5210c7393941619a23918ea2586879",
    #     "marketing_target": {"商品推广_日常推广": "3", "产品种草": "4", "商品推广_店铺拉新": "15"}
    # },  # 乐推（上海）文化传播有限公司
    "微思敦(宝宝馋了-酸奶)": {
        "account_id": "866725",
        "account": "ad_866725",
        "seller_id": "6604e05f7455790001a70a57",
        "marketing_target": {"商品推广_日常推广": "3", "产品种草": "4", "商品推广_店铺拉新": "15"}
    },  # 武汉微思敦网络技术有限公司
    "微思敦(宝宝馋了)": {
        "account_id": "861061",
        "account": "ad_861061",
        "seller_id": "6602447ef0b53500013ba010",
        "marketing_target": {"商品推广_日常推广": "3", "产品种草": "4", "商品推广_店铺拉新": "15"}
    },  # 武汉微思敦网络技术有限公司
    "炬辰(宝宝馋了辅食)": {
        "account_id": "1177643",
        "account": "ad_juchen_base",
        "seller_id": "664c3f6300444d0001166d99",
        "marketing_target": {"商品推广_日常推广": "3", "产品种草": "4", "商品推广_店铺拉新": "15"}
    },
    "小花猫(宝宝馋了)": {
        "account_id": "1798043",
        "account": "ad_1798043",
        "seller_id": "669f57debd86ee00153112e1",
        "marketing_target": {"商品推广_日常推广": "3", "产品种草": "4", "商品推广_店铺拉新": "15"}
    },  # 上海小花猫文化传播有限公司
    # "日弘传媒(宝宝馋了)": {
    #     "account_id": "1817413",
    #     "account": "ad_1817413",
    #     "seller_id": "66a1f047dcab6c00153660f8",
    #     "marketing_target": {"商品推广_日常推广": "3", "产品种草": "4", "商品推广_店铺拉新": "15"}
    # },  # 日弘传媒（上海）有限公司
    # # api里也需要配置
    "龙赢网络(宝宝馋了)": {
        "account_id": "1896481",
        "account": "ad_1896481",
        "seller_id": "66ab4c3ffaac8e001536c223",
        "marketing_target": {"商品推广_日常推广": "3", "产品种草": "4", "商品推广_店铺拉新": "15"}
    },  # 厦门龙赢网络技术服务有限公司
    "东信(宝宝馋了)": {
        "account_id": "6101018",
        "account": "ad_6101018",
        "seller_id": "675fcebe11448c00159d4977",
        "marketing_target": {"商品推广_日常推广": "3", "产品种草": "4", "商品推广_店铺拉新": "15"}
    },  # 深圳市东信时代信息技术有限公司
    "宝宝馋了-冲冲粥面": {
        "account_id": "7068078",
        "account": "ad_7068078",
        "seller_id": "67d2539013b3f400153d46cd",
        "marketing_target": {"商品推广_日常推广": "3", "产品种草": "4", "商品推广_店铺拉新": "15"}
    },  # 厦门龙赢网络技术服务有限公司
    
    
    "宝宝馋了-酸奶-LGL": {
        "account_id": "7839087",
        "account": "ad_7839087",
        "seller_id": "6864ae7e832e500015702d45",
        "marketing_target": {"商品推广_日常推广": "3", "产品种草": "4", "商品推广_店铺拉新": "15"}
    },  # 杭州众行壹策网络科技有限公司
    "宝宝馋了-冲冲粥面-小红盟-LGL": {
        "account_id": "7840116",
        "account": "ad_7840116",
        "seller_id": "6864c682294d990015903c7d",
        "marketing_target": {"商品推广_日常推广": "3", "产品种草": "4", "商品推广_店铺拉新": "15"}
    },  # 杭州众行壹策网络科技有限公司
    "宝宝馋了-冲冲粥面-LGL": {
        "account_id": "7837421",
        "account": "ad_7837421",
        "seller_id": "6864c6d618460a0015abfedc",
        "marketing_target": {"商品推广_日常推广": "3", "产品种草": "4", "商品推广_店铺拉新": "15"}
    },  # 杭州众行壹策网络科技有限公司
    "宝宝馋了酸奶-小红盟": {
        "account_id": "7403271",
        "account": "ad_7403271",
        "seller_id": "680882d4aeb07800155ff1a3",
        "marketing_target": {"商品推广_日常推广": "3", "产品种草": "4", "商品推广_店铺拉新": "15"}
    },  # 杭州星秀广告科技有限公司
}


def pagin(func):
    """
        分页装饰器
    """

    def wrapper(self, page, *args, **kwargs):
        is_next = func(self, page, *args, **kwargs)
        while is_next:
            page += 1
            is_next = func(self, page, *args, **kwargs)

    return wrapper


class XhsPutCost:
    def __init__(self, time_type="昨天"):
        self.spider_at = datetime.datetime.now()
        args = {"time_type": time_type, "timestamp_type": "秒", "time_format": "%Y-%m-%d"}
        get_time(args)
        self.time_tuple = args['time_tuple']
        self.dataframe = None
        logger.info('XhsGoodsDetail-获取时间为:', self.time_tuple)
        self.deleted_at = {"deleted_at": datetime.datetime.now()}
        self.ding_talk = DingTalk(webhook=config.get("ding_talk").get("webhook"),
                                  secret=AesEncryption(config.get("secret_key")).decryption(
                                      (config.get("ding_talk").get("secret")))
                                  )

    def get_xhs_put_cost(self):

        for account_name, account_info in AD_PROXIES_ACCOUNT.items():
            account = account_info['account']
            account_id = account_info['account_id']
            seller_id = account_info['seller_id']
            marketing_target = account_info['marketing_target']
            for target_name, target_id in marketing_target.items():
                logger.info(f"正在获取account_name:{account_name},target_name:{target_name}")
                self.dataframe = pd.DataFrame()
                json_data = {"vSellerId": seller_id,
                             "columns": ["noteId", "fee", "impression", "click", "ctr", "acp", "cpm", "like", "comment",
                                         "collect", "follow", "share", "interaction", "cpi", "actionButtonClick",
                                         "actionButtonCtr", "screenshot", "picSave", "reservePV", "searchCmtClick",
                                         "searchCmtClickCvr", "searchCmtAfterReadAvg", "searchCmtAfterRead",
                                         "wordAvgLocation",
                                         "wordImpressionRankFirst", "wordImpressionRateFirst",
                                         "wordImpressionRankThird",
                                         "wordImpressionRateThird", "wordClickRankFirst", "wordClickRateFirst",
                                         "wordClickRankThird", "wordClickRateThird", "rgmv"], "splitColumns": [],
                             "needTotal": True,
                             "needList": True, "needSize": True, "timeUnit": "DAY",
                             "pageSize": 200,
                             "sorts": [],
                             "reportType": "NOTE",
                             "startDate": self.time_tuple[0],
                             "endDate": self.time_tuple[1],
                             }
                if target_name == "全部":
                    json_data['filters'] = []
                else:
                    json_data['filters'] = [
                        {"column": "marketingTarget", "operator": "in", "values": [target_id]}]
                # logger.info(json_data)

                start_date = datetime.datetime.strptime(self.time_tuple[0], '%Y-%m-%d')
                end_date = datetime.datetime.strptime(self.time_tuple[1], '%Y-%m-%d') + datetime.timedelta(
                    hours=23, minutes=59, seconds=59)
                try:
                    self.request_xhs_put_cost(page=1, start_date=start_date,
                                              end_date=end_date,
                                              account=account,
                                              account_id=account_id,
                                              account_name=account_name,
                                              target_name=target_name,
                                              payload=json_data)
                    DasApplicationXhsPutCost.update(db=db, start_date=start_date,
                                                    end_date=end_date,
                                                    account_id=account_id, target_name=target_name,
                                                    data=self.deleted_at)

                    self.dataframe.to_sql(DasApplicationXhsPutCost.__tablename__, init_engine(), if_exists='append',
                                          index=False)
                    db.commit()
                except Exception as e:
                    db.rollback()
                    self.ding_talk.send_text(f"小红书投放数据获取失败:{account_name},{target_name},{e}")
                    return

            # break

    @pagin
    def request_xhs_put_cost(self, page, start_date, end_date, account, account_id, account_name, target_name, payload):
        """
        start_date:开始时间
        end_date:结束时间
        account:账号
        account_id:账号id
        account_name:账号名称
        target_name:营销目标
        """
        payload['pageNum'] = page
        is_success, data = XhsSpider().get_xhs_put_cost(account=account, payload=payload)
        if not is_success:
            raise Exception(f"小红书投放数据put_cost获取失败:{data}")
        rows = data.get('data', {}).get('list')
        if not rows:
            return False
        insert_data = []
        for row in rows:
            data_dict = {
                "start_date": start_date,  # 起始日期
                "end_date": end_date,  # 结束日期
                "account_id": account_id,  # 投放账号id
                "account_name": account_name,  # 投放账号名称
                "market_target": target_name,  # 营销目标
                "note_time": row.get("time"),  # 笔记日期
                "note_id": row.get('noteId', ""),  # 笔记id
                "note_title": row.get("noteTitle", ""),  # 笔记标题
                "pay_money": row.get("fee"),  # 支付金额
                "7_gmv": row.get("rgmv", "").replace("-", "0"),  # 7日下单金额
                "impression_num": row.get("impression"),  # 展现量
                "click_num": row.get("click"),  # 点击量
                "click_num_ratio": float(row.get("ctr", "0").replace('%', '')) / 100.0,  # 点击率
                "acp_num": row.get("acp"),  # 平均点击成本
                "cpm_num": row.get("cpm"),  # 平均千次展示费用
                "like_num": row.get("like"),  # 点赞
                "comment_num": row.get("comment"),  # 评论
                "collect_num": row.get("collect"),  # 收藏
                "follow_num": row.get("follow"),  # 关注
                "share_num": row.get("share"),  # 分享
                "interaction_num": row.get("interaction"),  # 互动量
                "cpi": row.get("cpi"),  # 平均互动成本
                "action_button_click": row.get("actionButtonClick"),  # 行动按钮点击量
                "action_button_ctr": float(row.get("actionButtonCtr", "0").replace('%', '')) / 100.0,  # 行动按钮点击率
                "screenshot_num": row.get("screenshot")  # 截图
            }
            insert_data.append(data_dict)
        self.dataframe = self.dataframe.append(pd.DataFrame(insert_data))
        totalPage = data.get('data', {}).get('totalPage')
        time.sleep(1.5)
        if page > totalPage:
            return False
        return True

    def run(self):
        self.get_xhs_put_cost()


if __name__ == '__main__':
    time_type = "昨天"
    xhs_put_cost = XhsPutCost(time_type=time_type)
    xhs_put_cost.run()
