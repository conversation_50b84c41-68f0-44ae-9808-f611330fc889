# encoding=utf-8
"""
小红书-种草文章获取
"""
import json
import pandas as pd
from datetime import datetime, timedelta, date
from xhs_apis.apis import XhsSpider
from utils.logx import logger
from collections import Counter
from xhs_data.database.database import create_app
from xhs_data.database.database import init_engine
from xhs_data.database.models import XhsGrassArticlesInfo
from utils.dingtalk import DingTalk
from xhs_data.settings import config
from utils.encryption import AesEncryption
from xhs_data.database.database import init_redis

def pagin(func):
    """
        分页装饰器
    """

    def wrapper(self, page, *args, **kwargs):
        is_next = func(self, page, *args, **kwargs)
        while is_next:
            page += 1
            is_next = func(self, page, *args, **kwargs)

    return wrapper


db = create_app()
rds = init_redis()


class XhsGrassArticles:
    def __init__(self):
        self.spider_at = datetime.now()
        logger.info('XhsGrassArticles-获取时间为:', self.spider_at)
        self.dataframe = pd.DataFrame()
        self.ding_talk = DingTalk(webhook=config.get("ding_talk").get("webhook"),
                                  secret=AesEncryption(config.get("secret_key")).decryption(
                                      (config.get("ding_talk").get("secret")))
                                  )

    def get_xhs_grass_articles(self):
        payload = {
            "operatorUserIds": [],
            "brandUserIds": [],
            "kolIds": [],
            "cooperateType": [],
            "dateType": "2",
            "bizTitle": "",
            "keyword": "",
            "noteId": "",
            "pageSize": 50,
            "sorts": [],
            "spuIds": [],
            "collectionTypeList": []
        }
        self.request_xhs_grass_articles(page=1, payload=payload)
        self.dataframe['spider_at'] = self.spider_at
        data_update_date = self.dataframe['data_update_date'].tolist()
        date_list = list(Counter(data_update_date))
        yesterday = datetime.now() - timedelta(days=1)
        yesterday_str = yesterday.strftime('%Y-%m-%d')
        # 查询数据更新时间为今天的进行软删
        if yesterday_str not in date_list:
            self.ding_talk.send_text(f"小红书-种草文章-数据更新时间不为昨天,未入库,文件里date_list:{date_list}")
            return
        try:
            now = date.today()
            to_day = datetime(now.year, now.month, now.day)
            to_day_str = to_day.strftime('%Y-%m-%d')
            select_gt_le_info_data = XhsGrassArticlesInfo.select_gt_le_info(db, to_day=to_day_str,yes_day=yesterday_str)
            if select_gt_le_info_data:
                data = {"deleted_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                XhsGrassArticlesInfo.update(db, to_day=to_day,yes_day=yesterday_str, data=data)
            self.dataframe.to_sql(XhsGrassArticlesInfo.__tablename__, init_engine(), if_exists='append', index=False)
            db.commit()
            logger.info("XHS-grass-articles-入库成功:", self.spider_at)
        #     print()
        #
        except Exception as e:
            db.rollback()
            logger.error("XHS-grass-articles-入库失败:", e)
            print()

    @pagin
    def request_xhs_grass_articles(self, page, payload):
        payload["pageNum"] = page
        is_success, data = XhsSpider().get_grass_articles(payload=payload)
        logger.info(f"XhsGrassArticles获取成功第{page}页")
        if not is_success:
            return False
        res_data = data.get('data')
        rows = res_data.get('list', None)
        if not rows:
            return False
        insert_data = []
        for row in rows:
            kol_credit_level, note_type, level, collection_type = None, None, None, None
            # 博主健康等级
            kol_credit_level_id = row.get('kolCreditLevel')
            if kol_credit_level_id == 1:
                kol_credit_level = '普通'
            elif kol_credit_level_id == 2:
                kol_credit_level = '优秀'
            elif kol_credit_level_id == 0:
                kol_credit_level = '异常'
            # 笔记类型
            note_type_id = row.get('noteType')
            if note_type_id == 1:
                note_type = '图文'
            elif note_type_id == 2:
                note_type = '视频'
            # 笔记来源？
            level_id = row.get('cooperateType')
            if level_id == 0:
                level = '定制合作'
            elif level_id == 3:
                level = '新芽合作'
            # 内容标签
            collection_type_id = row.get('collectionType')
            if collection_type_id == 1:
                collection_type = '单品'
            elif collection_type_id == 2:
                collection_type = '合集-默认'
            elif collection_type_id == 0:
                collection_type = None
            # 笔记栏组件数据
            note_bottom_comp_data = row.get('noteBottomCompData',{})
            with_note_bottom_comp = note_bottom_comp_data.get('withNoteBottomComp')
            note_bottom_comp_type = note_bottom_comp_data.get('noteBottomCompType')
            if with_note_bottom_comp == 1 and note_bottom_comp_type == 1:
                note_bottom_comp_type_str = "商品组件"
            else:
                note_bottom_comp_type_str = None

            note_id = row.get('noteId')
            if rds.hexists("xhs_grass_articles_link_queue", note_id):
                note_url = rds.hget("xhs_grass_articles_link_queue", note_id)
            else:
                is_success, note_data = XhsSpider().get_grass_articles_link(note_id)
                note_url = note_data.get('data').get('noteLink')
                rds.hset("xhs_grass_articles_link_queue", note_id, note_url)
            insert_item = {
                'data_update_date': row.get('dateKey'),  # 数据更新日期
                'kol_nick_name': row.get('kolNickName'),  # 博主昵称
                'kol_id': row.get('kolId'),  # 博主id
                'kol_url': "https://www.xiaohongshu.com/user/profile/" + row.get('kolId', ''),  # 博主主页链接
                'kol_fan_num': row.get('kolFanNum'),  # 博主粉丝
                'kol_credit_level': kol_credit_level,  # 博主健康等级(1普通/2健康)
                'note_title': row.get('noteTitle'),  # 笔记标题
                'note_url': note_url,  # 笔记id
                'note_type': note_type,  # 笔记类型(1图文/2视频),
                'note_publish_time': row.get('notePublishTime'),  # 笔记发布时间
                'level': level,  # 笔记来源(2定制合作)
                'note_id': row.get('noteId'),  # 笔记id
                'collection_type': collection_type,  # 内容标签
                'biz_title': row.get('bizTitle'),  # 合作名称
                'operate_user_name': row.get('operateUserName'),  # 下单账号
                'kol_price': row.get('kolPrice'),  # 博主报价
                'total_platform_price': row.get('totalPlatformPrice'),  # 服务费金额
                'spu_name': row.get('spuName'),  # spu名称
                'imp_num': row.get('impNum'),  # 曝光量
                'read_num': row.get('readNum'),  # 阅读量
                'read_uv_num': row.get('readUvNum'),  # 阅读UV
                'video_play_5s_rate': row.get('videoPlay5sRate'),  # 5s播放率
                'pic_read_3s_rate': row.get('picRead3sRate'),  # 3s阅读率
                'duration': row.get('duration'),  # 视频总时长
                'avg_viewtimed': row.get('avgViewTimeD'),  # 视频总时长
                # '': row.get(''),  # 视频完播率
                'engage_num': row.get('engageNum'),  # 互动量
                'engage_rate': row.get('engageRate'),  # 互动率
                'like_num': row.get('likeNum'),  # 点赞量
                'fav_num': row.get('favNum'),  # 收藏量
                'cmt_num': row.get('cmtNum'),  # 评论量
                'share_num': row.get('shareNum'),  # 分享量
                'follow_num': row.get('followCnt'),  # 关注量
                'origin_imp_num': row.get('originImpNum'),  # 自然曝光量
                'origin_read_num': row.get('originReadNum'),  # 自然阅读量
                'ads_imp_num': row.get('adsImpNum'),  # 推广曝光量
                'ads_effective_read_num': row.get('adsEffectiveReadNum'),  # 推广阅读量
                'heat_imp_num': row.get('heatImpNum'),  # 加热曝光量
                'heat_read_num': row.get('heatReadNum'),  # 加热阅读量
                'note_bottom_compType': note_bottom_comp_type_str,  # 笔记栏组件类型
                'note_bottom_comp_imp_num': note_bottom_comp_data.get("noteBottomCompImpNum"), # 笔记栏组件曝光量
                'note_bottom_comp_click_pv_num': note_bottom_comp_data.get("noteBottomCompClickPvNum"),  # 笔记底栏组件点击量
                'note_bottom_comp_click_uv_num': note_bottom_comp_data.get("noteBottomCompClickUvNum"),  # 笔记底栏组件点击人数
                'note_bottom_comp_click_ratio': note_bottom_comp_data.get("noteBottomCompClickRatio", 0)/100  # 底栏组件CTR
            }
            insert_data.append(insert_item)
        self.dataframe = self.dataframe.append(pd.DataFrame(insert_data))
        totalPage = data.get('totalPage')
        if totalPage == page:
            return False
        return True


if __name__ == '__main__':
    XhsGrassArticles().get_xhs_grass_articles()
